using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
{
    public class HUDTrainPassenger : HUDItemTickAble
    {
        public UIButton m_btnBuy;

        private EL_Train m_Train;

        void Start()
        {
            m_btnBuy.onClick.AddListener(OnBtnBuyClick);
        }

        protected override void OnInit(object param)
        {
            base.OnInit(param);
            m_Train = Owner as EL_Train;
            ColorLog.Green("火车位置", m_Train.transform.position);
            Refresh(m_Train.transform);
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
        }

        protected override void OnUpdate(float dt)
        {
            base.OnUpdate(dt);

            // 实时更新HUD位置以跟随火车移动
            if (m_Train != null)
            {
                Refresh(m_Train.transform);
            }
        }

        protected override Vector3 GetOffset()
        {
            // 基于EL_Train的位置叠加偏移量
            return new Vector3(9.2f, 3.2f, -0.98f);
        }

        void OnBtnBuyClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainContractForm);
        }
    }
}
