using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
{
    public class HUDTrainPassenger : HUDItemTickAble
    {
        public UIButton m_btnBuy;

        void Start()
        {
            m_btnBuy.onClick.AddListener(OnBtnBuyClick);
        }

        protected override void OnInit(object param)
        {
            base.OnInit(param);
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
        }

        protected override Vector3 GetOffset()
        {
            return new Vector3(9.2f, 3.2f, -0.98f);
        }

        void OnBtnBuyClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainContractForm);
        }
    }
}
