namespace Game.Hotfix
{
    public enum EnumHUD : int
    {
        /// <summary>
        /// 无
        /// </summary>
        None = 0,

        /// <summary>
        /// 建筑生产资源
        /// </summary>
        HUDProduceResource = 1,

        /// <summary>
        /// 建筑物升级或者建造
        /// </summary>
        HUDBuildingBuzy = 2,

        /// <summary>
        /// 建筑物收集资源提示
        /// </summary>
        HUDClaimResource = 3,

        /// <summary>
        /// 维修气泡
        /// </summary>
        HUDRepair = 4,

        /// <summary>
        /// 治疗气泡
        /// </summary>
        HUDSoldierTreat = 5,

        /// <summary>
        /// 士兵训练气泡
        /// </summary>
        HUDSoldierTrain = 6,

        /// <summary>
        /// pvePath 路径的解锁条件
        /// </summary>
        HUDPvePathLock = 7,

        /// <summary>
        /// 装备气泡
        /// </summary>
        HUDEquipment = 8,

        /// <summary>
        /// 商店气泡
        /// </summary>
        HUDShop = 9,

        /// <summary>
        /// 派遣气泡
        /// </summary>
        HUDBuildingDispatch = 10,

        /// <summary>
        /// 沙盘迁城 点击
        /// </summary>
        HUDWorldMapGridClick = 11,

        /// <summary>
        /// 联盟帮助气泡
        /// </summary>
        HUDUnionHelp = 12,

        /// <summary>
        /// 火车合约购买入口
        /// </summary>
        HUDTrainContract = 13,

        /// <summary>
        /// 无人机入口
        /// </summary>
        HUDUav = 14,

        /// <summary>
        /// 城墙消防气泡
        /// </summary>
        HUDOutfire = 15,

        /// <summary>
        /// 研究气泡
        /// </summary>
        HUDTech = 16,
        
        /// <summary>
        /// 火车乘客气泡
        /// </summary>
        HUDTrainPassenger = 17,
    }
}